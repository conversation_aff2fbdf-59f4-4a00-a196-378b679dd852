{"version": 3, "file": "single-character-alternation.js", "sourceRoot": "", "sources": ["../../../../src/linting/eslint/rules/single-character-alternation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;;;;GAkBG;AACH,+DAA+D;;;AAK/D,2CAAmD;AAEtC,QAAA,IAAI,GAAoB,IAAA,wBAAgB,EAAC,OAAO,CAAC,EAAE;IAC9D,SAAS,gBAAgB,CAAC,WAAwB;QAChD,MAAM,EAAE,YAAY,EAAE,GAAG,WAAW,CAAC;QACrC,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE;YAC5B,OAAO;SACR;QACD,IACE,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,EAC5F;YACA,OAAO,CAAC,gBAAgB,CAAC;gBACvB,OAAO,EAAE,kDAAkD;gBAC3D,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,UAAU,EAAE,WAAW;aACxB,CAAC,CAAC;SACJ;IACH,CAAC;IACD,OAAO;QACL,cAAc,EAAE,gBAAgB;QAChC,YAAY,EAAE,gBAAgB;QAC9B,qBAAqB,EAAE,gBAAgB;QACvC,gBAAgB,CAAC,IAAmB;YAClC,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE;gBAC3D,gBAAgB,CAAC,IAAmB,CAAC,CAAC;aACvC;QACH,CAAC;KACF,CAAC;AACJ,CAAC,CAAC,CAAC"}